package com.kerryprops.kip.audit;

import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;

import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.spring.LogbookClientHttpRequestInterceptor;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 审计系统自动配置类.
 * 负责扫描带有@AuditEntity注解的实体类，并动态为其注册EntityListener。
 *
 * <AUTHOR> Zhang
 */
@Slf4j
@AutoConfiguration
@ConditionalOnProperty(prefix = "audit", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(AuditProperties.class)
@EnableAsync
@EnableScheduling
public class AuditAutoConfiguration implements ApplicationContextAware {

    @Nullable
    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        log.info("AuditAutoConfiguration initialized with ApplicationContext: {}", applicationContext.getDisplayName());
    }

    // ========================= 基础组件 Bean 定义 =========================
    
    /**
     * 创建默认 Logbook 实例（当应用中不存在时）
     */
    @Bean
    @ConditionalOnMissingBean
    public Logbook logbook() {
        return Logbook.create();
    }

    /**
     * 创建HTTP请求工厂，配置超时时间
     */
    @Bean
    @ConditionalOnMissingBean
    public ClientHttpRequestFactory clientHttpRequestFactory(AuditProperties auditProperties) {
        var factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(auditProperties.getHttpClient().getConnectTimeout());
        factory.setReadTimeout(auditProperties.getHttpClient().getReadTimeout());
        return factory;
    }

    /**
     * 创建带 Logbook 支持的 RestTemplate
     */
    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate(Logbook logbook, ClientHttpRequestFactory requestFactory,
                                   List<HttpMessageConverter<?>> messageConverters,
                                   @Qualifier("auditObjectMapper") ObjectMapper auditObjectMapper) {
        var restTemplate = new RestTemplate(requestFactory);
        
        // 使用消费者的消息转换器列表，创建副本避免修改原始转换器
        var converters = messageConverters.stream()
                .map(converter -> {
                    // 对于 Jackson 转换器，创建使用审计专用 ObjectMapper 的新实例
                    if (converter instanceof MappingJackson2HttpMessageConverter jacksonConverter) {
                        var auditConverter = new MappingJackson2HttpMessageConverter(auditObjectMapper);
                        // 复制原转换器的媒体类型配置
                        auditConverter.setSupportedMediaTypes(jacksonConverter.getSupportedMediaTypes());
                        return auditConverter;
                    }
                    return converter;
                })
                .toList();
        
        restTemplate.setMessageConverters(converters);
        
        var interceptor = new LogbookClientHttpRequestInterceptor(logbook);
        restTemplate.setInterceptors(List.of(interceptor));
        
        // 设置默认请求头，强制使用 JSON 格式
        restTemplate.getInterceptors().add((request, body, execution) -> {
            request.getHeaders().setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
            return execution.execute(request, body);
        });
        
        return restTemplate;
    }

    @Bean
    @ConditionalOnMissingBean(name = "auditObjectMapper")
    public ObjectMapper auditObjectMapper() {
        return JsonUtils.JACKSON.getObjectMapper();
    }
    /**
     * 创建审计异步任务执行器
     */
    @Bean("auditTaskExecutor")
    @ConditionalOnMissingBean(name = "auditTaskExecutor")
    public Executor auditTaskExecutor() {
        var executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("audit-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    // ========================= 审计核心组件 Bean 定义 =========================

    /**
     * 创建实体缓存管理器
     */
    @Bean
    @ConditionalOnClass(EntityManagerFactory.class)
    public EntityCacheManager entityCacheManager(EntityManagerFactory entityManagerFactory) {
        return new EntityCacheManager(entityManagerFactory);
    }

    /**
     * 创建字段分析器
     */
    @Bean
    public FieldAnalyzer fieldAnalyzer(AuditProperties auditProperties) {
        return new FieldAnalyzer(auditProperties);
    }

    /**
     * 创建变更检测器
     */
    @Bean
    public ChangeDetector changeDetector(FieldAnalyzer fieldAnalyzer) {
        return new ChangeDetector(fieldAnalyzer);
    }

    /**
     * 创建审计事件工厂
     */
    @Bean
    public AuditEventFactory auditEventFactory(AuditProperties auditProperties, EntityCacheManager entityCacheManager) {
        return new AuditEventFactory(auditProperties, entityCacheManager);
    }

    /**
     * 创建审计客户端
     */
    @Bean
    public AuditClient auditClient(RestTemplate restTemplate, AuditProperties auditProperties) {
        return new AuditClient(restTemplate, auditProperties);
    }

    /**
     * 创建审计队列
     */
    @Bean
    @ConditionalOnBean(AuditClient.class)
    public AuditQueue auditQueue(AuditClient auditClient, AuditProperties auditProperties) {
        return new AuditQueue(auditClient, auditProperties);
    }

    /**
     * 创建审计服务
     */
    @Bean
    public AuditService auditService(EntityCacheManager entityCacheManager, ChangeDetector changeDetector, 
                                     AuditEventFactory auditEventFactory, AuditQueue auditQueue) {
        return new AuditService(entityCacheManager, changeDetector, auditEventFactory, auditQueue);
    }

    // ========================= Web 组件 Bean 定义 =========================

    /**
     * 创建审计用户上下文Web过滤器
     */
    @Bean
    public AuditUserContextWebFilter auditUserContextWebFilter(AuditProperties auditProperties) {
        return new AuditUserContextWebFilter(auditProperties);
    }

    // ========================= JPA 集成组件 Bean 定义 =========================

    /**
     * 创建审计实体扫描器Bean.
     *
     * @return AuditEntityScanner实例
     */
    @Bean
    public AuditEntityScanner auditEntityScanner() {
        return new AuditEntityScanner();
    }

    /**
     * 创建审计变更监听器Bean.
     *
     * @param auditService 审计服务
     * @return AuditChangeListener实例
     */
    @Bean
    public AuditChangeListener auditChangeListener(AuditService auditService) {
        return new AuditChangeListener(auditService);
    }

    /**
     * 创建审计实体后处理器Bean.
     * 负责注册全局Hibernate审计监听器.
     *
     * @param entityManagerFactory JPA实体管理器工厂
     * @param auditChangeListener  审计变更监听器
     * @param applicationContext   Spring应用上下文
     * @param auditEntityScanner   审计实体扫描器
     * @return AuditEntityPostProcessor实例
     */
    @Bean
    @ConditionalOnClass({ SessionFactoryImpl.class, EventListenerRegistry.class })
    public AuditEntityPostProcessor auditEntityPostProcessor(
            EntityManagerFactory entityManagerFactory,
            AuditChangeListener auditChangeListener,
            ApplicationContext applicationContext,
            AuditEntityScanner auditEntityScanner) {
        return new AuditEntityPostProcessor(entityManagerFactory, auditChangeListener,
                applicationContext, auditEntityScanner);
    }

}