package com.kerryprops.kip.audit;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import jakarta.annotation.PostConstruct;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * AuditProperties.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zhang 2025-07-01 17:49:35
 **/
@Slf4j
@Getter
@Setter
@ConfigurationProperties("audit")
public class AuditProperties {

    private int maxFieldLength = 1000;

    private Header header = new Header();

    private String url = "http://toolkit-service/api/audit/events/batch";

    private Set<String> excludedFields = new HashSet<>();

    /**
     * 全局字段别名映射配置.
     * key: 字段名, value: 显示别名
     */
    private Map<String, String> fieldAliases = new HashMap<>();

    /**
     * 全局默认包含策略.
     * 当实体未使用@AuditEntity注解或未指定defaultInclude时的默认行为
     */
    private boolean defaultInclude = true;

    @Value("${audit.service-id:${spring.application.name}}")
    private String serviceId;

    private String cron = "* * * * * ?";

    private long fixedDelay = 1000L;

    private Queue queue = new Queue();

    private HttpClient httpClient = new HttpClient();

    /**
     * 在属性加载完成后记录所有配置值
     */
    @PostConstruct
    public void logProperties() {
        log.debug("==== Audit Properties Configuration ====");
        log.debug("maxFieldLength: {}", maxFieldLength);
        log.debug("url: {}", url);
        log.debug("serviceId: {}", serviceId);
        log.debug("cron: {}", cron);
        log.debug("fixedDelay: {}", fixedDelay);
        log.debug("excludedFields: {}", excludedFields);
        log.debug("fieldAliases: {}", fieldAliases);
        log.debug("defaultInclude: {}", defaultInclude);
        log.debug("header: {}", header);
        log.debug("queue: {}", queue);
        log.debug("httpClient: {}", httpClient);
        log.debug("========================================");
    }

    @Getter
    @Setter
    @ToString
    public static class Queue {
        private int capacity = 10000;
        private int batchSize = 100;
    }

    @Getter
    @Setter
    @ToString
    public static class HttpClient {
        private int connectTimeout = 5000;
        private int readTimeout = 10000;
    }

    @Getter
    @Setter
    @ToString
    public static class Header {

        private String user = "X-User";

        private String uiModel = "X-UI-Model";

        private String conversationId = "X-Conversation-Id";

        private String correlationId = "X-Correlation-ID";

        /**
         * 审计过滤key
         */
        private String auditFilterKey = "X-Audit-Filter-Key";
        /**
         * 审计过滤value
         */
        private String auditFilterValue = "X-Audit-Filter-Value";

    }

}
