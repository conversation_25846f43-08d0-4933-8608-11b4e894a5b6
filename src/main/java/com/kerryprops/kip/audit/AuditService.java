package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@RequiredArgsConstructor
public class AuditService {

    private final EntityCacheManager entityCacheManager;
    private final ChangeDetector changeDetector;
    private final AuditEventFactory auditEventFactory;
    private final AuditQueue auditQueue;

    /**
     * 在实体更新前调用，缓存实体原始状态
     *
     * @param entity 待更新的实体
     */
    public void beforeUpdate(Object entity) {
        entityCacheManager.cacheOriginalEntity(entity);
    }

    public void recordCreation(Object entity) {
        try {
            var auditLog = auditEventFactory.createAuditEventRequest(entity, AuditOperation.CREATE);
            var fieldChanges = changeDetector.detectChanges(null, entity);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity creation: ", e);
        }
    }

    public void recordUpdate(Object entity) {
        try {
            var oldEntity = entityCacheManager.getOriginalEntity(entity);
            var auditLog = auditEventFactory.createAuditEventRequest(entity, AuditOperation.UPDATE);
            var fieldChanges = changeDetector.detectChanges(oldEntity, entity);
            if(fieldChanges.isEmpty()) {
                log.warn("No field changes detected for entity: {}", entity);
                return; // 如果没有字段变化，则不记录审计日志
            }
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity update: ", e);
        }
    }

    public void recordDeletion(Object entity) {
        try {
            AuditEventRequest auditLog = auditEventFactory.createAuditEventRequest(entity, AuditOperation.DELETE);
            var fieldChanges = changeDetector.detectChanges(entity, null);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity deletion: ", e);
        }
    }

}