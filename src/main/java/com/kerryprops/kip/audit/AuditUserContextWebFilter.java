package com.kerryprops.kip.audit;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Optional;
import java.util.UUID;

import static com.kerryprops.kip.audit.AppConstants.USER_AGENT;

/**
 * HTTP interceptor that extracts user information from request headers
 * and sets up the audit context for the current request.
 * This interceptor looks for the configured user header (default: x-user)
 * and parses the JSON content into a LoginUserInfo object, making it
 * available to the audit system throughout the request lifecycle.
 *
 * <AUTHOR> Zhang
 */
@Slf4j
@RequiredArgsConstructor
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
public class AuditUserContextWebFilter extends OncePerRequestFilter {
    private static final String UNKNOWN = "unknown";

    private final AuditProperties auditProperties;

    private static String getHeaderOrGenerateUuid(HttpServletRequest request, String headerName) {
        return Optional.ofNullable(request.getHeader(headerName))
                .filter(value -> !value.isBlank())
                .orElse(UUID.randomUUID().toString());
    }

    private static String getCorrelationId(HttpServletRequest request, AuditProperties.Header header) {
        return getHeaderOrGenerateUuid(request, header.getCorrelationId());
    }

    private static String getConversationId(HttpServletRequest request, AuditProperties.Header header) {
        return getHeaderOrGenerateUuid(request, header.getConversationId());
    }

    private static Runnable createAnonymous() {
        return () -> {
            log.warn("Creating anonymous user, this is a security risk!!!");
            AuditContext.storeCurrentUser(XuserInfo.ANONYMOUS_USER);
        };
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // 提取用户信息并设置到上下文
        processUserContext(request);

        // 构建并验证审计请求信息
        var auditRequestInfo = buildAuditRequestInfo(request);
        AuditContext.storeAuditRequestInfo(auditRequestInfo);

        try {
            filterChain.doFilter(request, response);
        } finally {
            // 清理审计上下文防止内存泄漏
            AuditContext.clear();
            log.info("Cleared audit context for request: {} {}", request.getMethod(), request.getRequestURI());
        }
    }

    private void processUserContext(HttpServletRequest request) {
        var userHeader = auditProperties.getHeader().getUser();
        var userJson = request.getHeader(userHeader);
        if (!StringUtils.hasText(userJson)) {
            log.warn("No user header '{}' found in request, using anonymous user, this is a security risk!!!", userHeader);
            userJson = Strings.EMPTY;
        }
        JsonUtils.fromJson(userJson, XuserInfo.class)
                .ifPresentOrElse(AuditContext::storeCurrentUser, createAnonymous());
    }

    /**
     * Builds AuditRequestInfo using functional programming principles.
     * This method eliminates mutable state and uses pure functions with method chaining.
     *
     * @param request HttpServletRequest to extract information from
     * @return AuditRequestInfo populated with request data
     */
    private AuditRequestInfo buildAuditRequestInfo(HttpServletRequest request) {
        var header = auditProperties.getHeader();

        return AuditRequestInfoBuilder.create()
                .withIpAddress(getIpAddress(request))
                .withConversationId(getConversationId(request, header))
                .withCorrelationId(getCorrelationId(request, header))
                .withUserAgent(extractHeaderWithDefault(request, USER_AGENT, UNKNOWN))
                .withUiModel(extractHeaderWithDefault(request, header.getUiModel(), "default"))
                .withAuditFilterKey(extractHeaderWithDefault(request, header.getAuditFilterKey(), "none"))
                .withAuditFilterValue(extractHeaderWithDefault(request, header.getAuditFilterValue(), "none"))
                .build();
    }

    /**
     * Pure function to extract header value with default fallback.
     * Uses Optional for null safety and functional composition.
     *
     * @param request HttpServletRequest to extract header from
     * @param headerName Name of the header to extract
     * @param defaultValue Default value if header is missing or empty
     * @return Header value or default value
     */
    private static String extractHeaderWithDefault(HttpServletRequest request, String headerName, String defaultValue) {
        return Optional.ofNullable(request.getHeader(headerName))
                .filter(StringUtils::hasText)
                .orElse(defaultValue);
    }

    /**
     * Functional builder for AuditRequestInfo following immutable construction pattern.
     * Each method returns a new builder instance, eliminating mutable state.
     * Uses Java record for concise immutable data structure.
     */
    private record AuditRequestInfoBuilder(String ipAddress, String userAgent, String conversationId,
                                           String correlationId, String uiModel, String auditFilterKey,
                                           String auditFilterValue) {

        /**
         * Creates a new builder with empty string defaults to avoid null values.
         * Empty strings will be replaced with actual values through the with* methods.
         */
        public static AuditRequestInfoBuilder create() {
            return new AuditRequestInfoBuilder("", "", "", "", "", "", "");
        }
    
            public AuditRequestInfoBuilder withIpAddress(String ipAddress) {
                return new AuditRequestInfoBuilder(ipAddress, this.userAgent, this.conversationId,
                        this.correlationId, this.uiModel, this.auditFilterKey,
                        this.auditFilterValue);
            }
    
            public AuditRequestInfoBuilder withUserAgent(String userAgent) {
                return new AuditRequestInfoBuilder(this.ipAddress, userAgent, this.conversationId,
                        this.correlationId, this.uiModel, this.auditFilterKey,
                        this.auditFilterValue);
            }
    
            public AuditRequestInfoBuilder withConversationId(String conversationId) {
                return new AuditRequestInfoBuilder(this.ipAddress, this.userAgent, conversationId,
                        this.correlationId, this.uiModel, this.auditFilterKey,
                        this.auditFilterValue);
            }
    
            public AuditRequestInfoBuilder withCorrelationId(String correlationId) {
                return new AuditRequestInfoBuilder(this.ipAddress, this.userAgent, this.conversationId,
                        correlationId, this.uiModel, this.auditFilterKey,
                        this.auditFilterValue);
            }
    
            public AuditRequestInfoBuilder withUiModel(String uiModel) {
                return new AuditRequestInfoBuilder(this.ipAddress, this.userAgent, this.conversationId,
                        this.correlationId, uiModel, this.auditFilterKey,
                        this.auditFilterValue);
            }
    
            public AuditRequestInfoBuilder withAuditFilterKey(String auditFilterKey) {
                return new AuditRequestInfoBuilder(this.ipAddress, this.userAgent, this.conversationId,
                        this.correlationId, this.uiModel, auditFilterKey,
                        this.auditFilterValue);
            }
    
            public AuditRequestInfoBuilder withAuditFilterValue(String auditFilterValue) {
                return new AuditRequestInfoBuilder(this.ipAddress, this.userAgent, this.conversationId,
                        this.correlationId, this.uiModel, this.auditFilterKey,
                        auditFilterValue);
            }
    
            public AuditRequestInfo build() {
                var auditRequestInfo = new AuditRequestInfo();
                auditRequestInfo.setIpAddress(this.ipAddress);
                auditRequestInfo.setUserAgent(this.userAgent);
                auditRequestInfo.setConversationId(this.conversationId);
                auditRequestInfo.setCorrelationId(this.correlationId);
                auditRequestInfo.setUiModel(this.uiModel);
                auditRequestInfo.setAuditFilterKey(this.auditFilterKey);
                auditRequestInfo.setAuditFilterValue(this.auditFilterValue);
                return auditRequestInfo;
            }
        }

    /**
     * 获取客户端IP地址
     * 由于客户端的IP地址可能通过多个代理层转发，因此需要检查多个HTTP头字段以获取真实IP。
     * 此方法首先检查“x-forwarded-for”头，这是最常用的代理头，然后尝试其他不那么常见的头字段。
     * 如果所有尝试都失败，则回退到使用请求的远程地址。
     *
     * @param request HttpServletRequest对象，用于获取客户端IP地址。
     * @return 客户端的IP地址字符串。如果无法确定客户端IP，则返回请求的远程地址。
     */
    private String getIpAddress(HttpServletRequest request) {
        // 尝试获取“x-forwarded-for”头，这是最常用的代理头字段。
        var ip = request.getHeader("x-forwarded-for");
        if (StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip)) {
            ip = ip.contains(",") ? ip.split(",")[0] : ip;
        }
        // 如果“x-forwarded-for”头无效，尝试其他不那么常见的代理头字段。
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            log.warn("No IP address found in request headers, using 0.0.0.0");
            ip = "0.0.0.0";
        }
        log.info("Client IP address: {}.", ip);
        return ip;
    }

}
