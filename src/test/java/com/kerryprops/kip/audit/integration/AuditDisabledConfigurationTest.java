package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.AuditService;
import com.kerryprops.kip.audit.AuditClient;
import com.kerryprops.kip.audit.AuditQueue;
import com.kerryprops.kip.audit.AuditAutoConfiguration;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试审计功能禁用配置
 */
@SpringBootTest(classes = AuditDisabledConfigurationTest.TestApplication.class)
@TestPropertySource(properties = {
        "audit.enabled=false"
})
@DisplayName("审计功能禁用时配置测试") 
class AuditDisabledConfigurationTest {

    @org.springframework.boot.autoconfigure.SpringBootApplication
    static class TestApplication {
        // 最小化的测试应用，专门用于测试审计禁用功能
    }

    @Test
    @DisplayName("审计功能禁用时应该能正常启动应用")
    void shouldStartApplicationSuccessfullyWhenAuditDisabled(ApplicationContext applicationContext) {
        // 验证应用上下文能够正常启动
        assertThat(applicationContext).isNotNull();
        
        // 验证核心审计Bean不会被创建
        assertThat(applicationContext.getBeansOfType(AuditService.class))
                .as("AuditService beans should not be created when audit is disabled")
                .isEmpty();
        
        assertThat(applicationContext.getBeansOfType(AuditClient.class))
                .as("AuditClient beans should not be created when audit is disabled")
                .isEmpty();
        
        assertThat(applicationContext.getBeansOfType(AuditQueue.class))
                .as("AuditQueue beans should not be created when audit is disabled")
                .isEmpty();
        
        // 验证审计自动配置不应该被激活
        assertThat(applicationContext.getBeansOfType(AuditAutoConfiguration.class))
                .as("AuditAutoConfiguration should not be created when audit is disabled")
                .isEmpty();
    }
}
