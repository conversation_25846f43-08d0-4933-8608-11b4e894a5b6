package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.AuditAutoConfiguration;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试包扫描功能
 */
@SpringBootTest(classes = PackageScanConfigurationTest.TestApplication.class)
@TestPropertySource(properties = {
        "audit.enabled=true"
})
@DisplayName("包扫描功能测试")
class PackageScanConfigurationTest extends BaseIntegrationTest {

    @org.springframework.boot.autoconfigure.SpringBootApplication(scanBasePackages = "com.kerryprops.kip.audit")
    @EntityScan(basePackages = {"com.kerryprops.kip.audit.integration", "com.kerryprops.kip.audit.test"})
    static class TestApplication {
    }


    @Test
    @DisplayName("应该从@EntityScan注解获取扫描包路径")
    void shouldGetScanPackagesFromEntityScanAnnotation(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证自动配置能够正确处理多个扫描包
        // 通过不抛出异常来验证包扫描逻辑正常工作
    }

    @Test
    @DisplayName("应该使用默认扫描包当没有找到@EntityScan时")
    void shouldUseDefaultScanPackagesWhenNoEntityScanFound(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证默认扫描逻辑正常工作
    }
}
