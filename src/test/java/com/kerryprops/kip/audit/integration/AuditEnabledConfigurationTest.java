package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试审计功能默认启用配置
 */
@SpringBootTest(classes = AuditEnabledConfigurationTest.TestApplication.class)
@TestPropertySource(properties = {
        "audit.enabled=true",
        "audit.max-field-length=500",
        "audit.service-id=test-service"
})
@DisplayName("审计功能启用时配置测试")
class AuditEnabledConfigurationTest extends BaseIntegrationTest {

    @SpringBootApplication(scanBasePackages = "com.kerryprops.kip")
    @EntityScan(basePackages = "com.kerryprops.kip.audit.integration")
    @Import(AuditAutoConfiguration.class)
    static class TestApplication {
    }


    @Test
    @DisplayName("审计功能启用时应该创建所有必要的Bean")
    void shouldCreateAllRequiredBeansWhenAuditEnabled(ApplicationContext applicationContext) {
        // 验证自动配置类被加载
        assertThat(applicationContext.getBean(AuditAutoConfiguration.class))
                .isNotNull();

        // 验证核心审计组件被创建
        assertThat(applicationContext.getBean(AuditService.class))
                .isNotNull();

        assertThat(applicationContext.getBean(AuditQueue.class))
                .isNotNull();

        assertThat(applicationContext.getBean(AuditChangeListener.class))
                .isNotNull();

        // 验证配置属性被正确注入
        var auditProperties = applicationContext.getBean(AuditProperties.class);
        assertThat(auditProperties.getMaxFieldLength()).isEqualTo(500);
        assertThat(auditProperties.getServiceId()).isEqualTo("test-service");
    }

    @Test
    @DisplayName("AuditChangeListener应该依赖AuditService正确创建")
    void shouldCreateAuditChangeListenerWithAuditServiceDependency(ApplicationContext applicationContext) {
        var auditChangeListener = applicationContext.getBean(AuditChangeListener.class);
        assertThat(auditChangeListener).isNotNull();

        // 验证依赖注入正确
        var auditService = applicationContext.getBean(AuditService.class);
        assertThat(auditService).isNotNull();
    }
}
