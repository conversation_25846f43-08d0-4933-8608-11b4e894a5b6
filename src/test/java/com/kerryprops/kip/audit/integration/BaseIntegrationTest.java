package com.kerryprops.kip.audit.integration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * BaseIntegrationTest.
 *
 * <AUTHOR> 2025-04-23 16:09:40
 **/
@ActiveProfiles("test")
@SpringBootTest(classes = BaseIntegrationTest.ApplicationLauncher.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class BaseIntegrationTest {

    @SpringBootApplication(scanBasePackages = "com.kerryprops.kip.audit")
    public static class ApplicationLauncher {

        public static void main(String[] args) {
            SpringApplication.run(ApplicationLauncher.class, args);
        }

    }

}
