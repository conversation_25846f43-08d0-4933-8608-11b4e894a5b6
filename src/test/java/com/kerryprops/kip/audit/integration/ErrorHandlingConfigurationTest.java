package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 错误处理场景测试.
 * 测试审计系统在异常情况下的错误处理机制
 *
 * <AUTHOR> Zhang
 */
@SpringBootTest(classes = ErrorHandlingConfigurationTest.TestApplication.class)
@TestPropertySource(properties = {
        "audit.enabled=true",
        "logging.level.com.kerryprops.kip.audit=DEBUG"
})
@DisplayName("错误处理场景测试")
class ErrorHandlingConfigurationTest extends BaseIntegrationTest {

    @org.springframework.boot.autoconfigure.SpringBootApplication(scanBasePackages = "com.kerryprops.kip.audit")
    @Import(AuditAutoConfiguration.class)
    static class TestApplication {
    }

    @Test
    @DisplayName("应该优雅处理实体扫描过程中的异常")
    void shouldGracefullyHandleExceptionsDuringEntityScanning(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证即使扫描过程中遇到一些异常，应用仍能正常启动
        // 这个测试主要验证错误处理机制不会导致应用启动失败
    }

    @Test
    @DisplayName("应该正确记录实体监听器注册信息")
    void shouldLogEntityListenerRegistrationInfo(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证自动配置类正确初始化
        // 实际的日志验证在实际运行时可以通过日志输出观察
    }
}