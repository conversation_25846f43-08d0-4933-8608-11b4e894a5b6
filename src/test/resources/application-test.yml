spring:
  application:
    name: audit-spring-boot-starter
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
audit:
  max-field-length: 1000
  excluded-fields:
    - password
    - token
    - secret
  header:
    user: x-user
  url: http://localhost:8089/api/audit/events/batch
  service-id: ${spring.application.name}
  cron: "* * * * * ?"
logging:
  level:
    org.zalando.logbook: trace